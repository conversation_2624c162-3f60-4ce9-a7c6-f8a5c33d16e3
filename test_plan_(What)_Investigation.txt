Of course. Here is a detailed test plan for evaluating the described control, structured for an audit or compliance analyst. The plan expands on your provided points and incorporates the full context of the control description to ensure comprehensive testing.

---

### **Test Plan: Duplicate Payment Prevention Control**

**Control Objective:** To ensure that systemically identified potential duplicate payments are investigated, resolved, and accurately dispositioned in a complete and timely manner before payment release, with appropriate escalation when required.

**Sample Size:** 24 transactions flagged as potential duplicates within the audit period.

---

### **1. Completeness Testing**

**Objective:** To verify that all required steps in the duplicate payment review process are executed and that a complete, undeniable audit trail is created for each flagged transaction.

---

#### **Test 1.1: Verification of Anomaly Logging and Initial Triage**

**Purpose:** To confirm that when the system identifies a potential duplicate, it is correctly logged in the system with the appropriate status and initial details for an analyst to begin their review.

| # | Test Step | Expected Outcome | Evidence to Collect |
|---|---|---|---|
| 1 | Obtain a population of all payments flagged as potential duplicates by the system for the audit period. From this population, randomly select a sample of 24 transactions. | A list of 24 unique transaction IDs is generated. | A documented list of the 24 selected sample transaction IDs. |
| 2 | For each of the 24 sample transactions, navigate to the transaction details page in the payment system. | The analyst can successfully access the full details of each transaction. | Screenshot of the main transaction screen for one sample as representative evidence of access. |
| 3 | Locate the "Audit Trail" or "History Log" for the transaction. Verify that an entry exists with an activity status of **"Pending Anomaly Approval"**. | All 24 samples must show the "Pending Anomaly Approval" status at the appropriate point in their lifecycle. | Screenshot of the audit trail for any sample where the status is missing. A summary table noting "Pass/Fail" for all 24 samples. |
| 4 | Within the transaction details, locate the **"Remarks"** or "Comments" section. Verify that a system-generated comment exists, such as **"Anomaly found of ref #"** followed by the reference number of the potential original payment. | All 24 samples must contain this system-generated remark, confirming the system properly identified and linked the potential duplicate. | Screenshot of the remarks section showing the specific text for 3-5 samples. Note any deviations. |

---

#### **Test 1.2: Verification of Analyst Action and Accountability**

**Purpose:** To ensure that an analyst is formally assigned to the task and that their subsequent actions are captured in the audit trail.

| # | Test Step | Expected Outcome | Evidence to Collect |
|---|---|---|---|
| 1 | For the same 24 samples, locate the section that indicates user assignment. Per the control description, this may be under a workflow status like **"Pending Scan"** or in a field named "Assigned Analyst". | Each of the 24 flagged payments must have a valid, non-null user name or user ID assigned to it. An unassigned alert is a control failure. | Screenshot of the user assignment field for 3-5 samples. Note any samples that are unassigned. |
| 2 | Review the audit trail for each sample again. Following the initial "Pending Anomaly Approval" status, look for an analyst-driven entry that confirms their review. | The audit trail must contain a subsequent entry indicating the analyst's action (e.g., adding remarks like "Confirmation of re #", "False positive, cleared for payment", or "Confirmed duplicate, cancelling"). | Screenshot of the analyst-driven entry in the audit trail for 3-5 samples. |
| 3 | Verify that the audit trail for the analyst's action (as identified in Step 2) includes a specific **date and time stamp**. | All 24 samples must show a non-null, valid date and time stamp for when the analyst populated their findings in the remarks/audit log. | A summary table confirming the presence of timestamps for all 24 samples. Screenshot any exceptions. |

---

### **2. Timeliness Testing**

**Objective:** To verify that potential duplicate payments are assigned and reviewed promptly to prevent delays in the payment cycle for legitimate transactions.

---

#### **Test 2.1: Timeliness of Analyst Assignment**

**Purpose:** To validate that alerts are assigned to an analyst on the same day they are generated by the system.

| # | Test Step | Expected Outcome | Evidence to Collect |
|---|---|---|---|
| 1 | Using the same 24 samples, open the audit trail for each transaction. | The analyst can access the audit trail for all samples. | N/A |
| 2 | Identify the timestamp (date and time) for the system-generated status: **"Pending Anomaly Approval"**. Record the date. | A clear date is recorded for each of the 24 samples. | A working paper or spreadsheet with columns for "Transaction ID", "Anomaly Date", and "Assignment Date". |
| 3 | Identify the timestamp for the status indicating analyst assignment, such as **"Pending Scan"** or the first entry made by the assigned analyst. Record the date. | A clear date is recorded for each of the 24 samples. | Populate the "Assignment Date" column in the working paper for all 24 samples. |
| 4 | Compare the "Anomaly Date" (from Step 2) and the "Assignment Date" (from Step 3) for each sample. | For all 24 samples, the **date must be equal**. A difference in days indicates a delay in assigning the alert for review. | The completed working paper comparing the dates. Highlight any samples where the dates are not equal and provide screenshots of the relevant audit trails as evidence. |

---

### **3. Accuracy Testing**

**Objective:** To verify that the analyst's investigation is thorough, the final disposition (release/cancel) is correct, and the escalation procedure is followed when necessary.

---

#### **Test 3.1: Accuracy of Analyst Disposition**

**Purpose:** To independently re-perform the analyst's review to confirm their conclusion was correct. For this test, select a subsample of 12 payments that were **released** and 12 that were **cancelled**.

| # | Test Step | Expected Outcome | Evidence to Collect |
|---|---|---|---|
| 1 | **For the 12 Released Payments:** Review the analyst's notes and the payment details (e.g., invoice #, vendor, amount, date). Independently search the payment history to confirm that no other payment with the same core attributes was processed. | The auditor's independent check should confirm that the payment was a "false positive" and not a true duplicate. | Auditor's notes in the working paper for each sample, stating "Conclusion: Analyst disposition was correct/incorrect" with a brief justification. |
| 2 | **For the 12 Cancelled Payments:** Review the analyst's notes and the reference number of the original payment. Independently locate the original payment and compare it to the cancelled one. | The auditor's independent check should confirm that the cancelled payment was a "true positive" and was correctly identified as a duplicate of the original. | Auditor's notes in the working paper for each sample, stating "Conclusion: Analyst disposition was correct/incorrect" with a brief justification. |

---

#### **Test 3.2: Verification of Escalation Procedure**

**Purpose:** To confirm that high-risk or ambiguous cases are escalated to senior management according to business procedures.

| # | Test Step | Expected Outcome | Evidence to Collect |
|---|---|---|---|
| 1 | Inquire with management to define the criteria for mandatory escalation (e.g., all potential duplicates over $100,000, payments to a specific type of vendor, or cases where an analyst is uncertain). | A clear, documented policy for escalation is provided. | A copy of the escalation policy or documented meeting minutes confirming the criteria. |
| 2 | From the full population of flagged payments, select a sample of 5-10 transactions that met the criteria for mandatory escalation. | A list of 5-10 high-risk transaction IDs is generated. | A documented list of the selected high-risk sample IDs. |
| 3 | For each high-risk sample, review the audit trail, comments, and any attached communications. Look for evidence of escalation, such as a status change to **"Pending Management Approval"** or a comment like "Escalated to [Manager's Name]". | All selected high-risk samples must show clear evidence of having been escalated to senior management. | Screenshot of the escalation evidence from the system for each sample tested. |
| 4 | Verify that the evidence of senior management's decision (e.g., approval to release, confirmation to cancel) is documented in the system **before** the final action was taken by the payment analyst. | The management approval timestamp must precede the final disposition timestamp in the audit trail. | Screenshot of the audit trail showing the sequence of management approval followed by the final payment analyst action. Note any exceptions. |