import streamlit as st
import google.generativeai as genai
import os
import pandas as pd
from typing import Optional
from reportlab.lib.pagesizes import letter
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from io import BytesIO
import re

# Configure page
st.set_page_config(
    page_title="Test Plan Generator",
    page_icon="🧪",
    layout="wide",
    initial_sidebar_state="expanded"
)

def configure_gemini_api(api_key: str) -> bool:
    """Configure Gemini API with the provided key"""
    try:
        genai.configure(api_key=api_key)
        return True
    except Exception as e:
        st.error(f"Error configuring Gemini API: {str(e)}")
        return False

def create_pdf(test_plan: str, control_description: str) -> BytesIO:
    """Create a PDF from the test plan"""
    buffer = BytesIO()
    doc = SimpleDocTemplate(buffer, pagesize=letter, rightMargin=72, leftMargin=72, topMargin=72, bottomMargin=18)
    
    # Define styles
    styles = getSampleStyleSheet()
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=16,
        spaceAfter=30,
    )
    heading_style = ParagraphStyle(
        'CustomHeading',
        parent=styles['Heading2'],
        fontSize=14,
        spaceAfter=12,
    )
    
    # Build the PDF content
    story = []
    
    # Title
    story.append(Paragraph("Test Plan Generator Report", title_style))
    story.append(Spacer(1, 12))
    
    # Control Description
    story.append(Paragraph("Control Description:", heading_style))
    story.append(Paragraph(control_description, styles['Normal']))
    story.append(Spacer(1, 12))
    
    # Test Plan
    story.append(Paragraph("Generated Test Plan:", heading_style))
    
    # Convert markdown-style text to paragraphs
    lines = test_plan.split('\n')
    for line in lines:
        if line.strip():
            if line.startswith('###'):
                # Heading
                story.append(Paragraph(line.replace('###', '').strip(), heading_style))
            elif line.startswith('##'):
                # Sub-heading
                story.append(Paragraph(line.replace('##', '').strip(), heading_style))
            elif line.startswith('- '):
                # Bullet point
                story.append(Paragraph(line, styles['Normal']))
            else:
                # Regular paragraph
                story.append(Paragraph(line, styles['Normal']))
        else:
            story.append(Spacer(1, 6))
    
    # Build PDF
    doc.build(story)
    buffer.seek(0)
    return buffer

def generate_test_plan(control_description: str, context_data: str = None) -> Optional[str]:
    """Generate test plan using Gemini API"""
    try:
        # Hardcoded API key
        api_key = "AIzaSyD6sL6Xnci-7ueYraCiRmeuvmhrb76rLEA"
        
        # Configure the API
        genai.configure(api_key=api_key)
        
        # Create the model - try different model names based on version
        try:
            model = genai.GenerativeModel("gemini-2.5-pro")
        except:
            # Fallback for older versions
            model = genai.GenerativeModel("gemini-2.5-pro")
        
        # Create the prompt with optional context data
        context_section = ""
        if context_data:
            context_section = f"""
            
### Additional Context Data:
The following CSV data provides additional context for generating the test plan:

{context_data[:3000]}  # Limit context to prevent token overflow

Please use this context data to make the test steps more specific and relevant.
"""
        
        # Create the prompt
        prompt = f"""
Based on the following control description: "{control_description}"

Generate detailed test steps for evaluating the control across the following 3 aspects: **completeness**, **timeliness**, and **accuracy**.

Each test step should be clearly structured and executable by an audit or compliance analyst.

Use the following examples as context for the type of test cases expected:

### Completeness:
- Verify that in the audit trail, under activity status "pending anomaly approval", there is a "duplicate" flag under the "remarks" section for the "confirmation of ref #".
- Verify that the audit trail captures the date and time when the duplicate flag was raised.
- Confirm that under the section "pending scan", there is an employee name mentioned to confirm the investigation and disposal of the flagged duplicate payment.

### Timeliness:
- Compare the dates in the audit trail under "pending anomaly approval" and "pending scan" statuses.
- Validate that both events occurred on the same day to ensure the investigation and payment disposal happened promptly.

{context_section}

Now, based on the control description provided, generate comprehensive test steps that follow this structure and style. Include specific, actionable steps for each aspect:

### Completeness:
[Generate specific test steps for completeness based on the control description]

### Timeliness:
[Generate specific test steps for timeliness based on the control description]

### Accuracy:
[Generate specific test steps for accuracy based on the control description]

Please provide detailed, executable test steps that an auditor can follow.
        """
        
        # Generate the response
        response = model.generate_content(prompt)
        return response.text
    
    except Exception as e:
        st.error(f"Error generating test plan: {str(e)}")
        return None

def main():
    # Title and header
    st.title("🧪 Test Plan Generator")
    st.markdown("---")
    st.markdown("Generate comprehensive test plans for audit controls using AI")
    st.markdown("---")
    
    # Sidebar for API configuration
    with st.sidebar:
        st.header("⚙️ Configuration")
        
        st.success("API Key: Configured ✅")
        
        st.markdown("---")
        st.markdown("### 📋 Instructions")
        st.markdown("""
        1. Describe the control you want to test
        2. Optionally upload a CSV file with context data
        3. Click 'Generate Test Plans'
        4. Review the comprehensive test plan
        5. Download as Text or PDF
        """)
    
    # Main content area
    col1, col2 = st.columns([1, 1])
    
    with col1:
        st.header("📝 Control Description")
        
        # Control description input
        control_description = st.text_area(
            "Enter Control Description",
            value="(What) Investigation and resolution of systemically identified potential duplicate payments before release (How) Payment system identifies potential duplicate payments based on defined logic – Payment analyst reviews the systemic notification and disposition the outcome following business procedures. If required, payment analyst escalates potential duplicate to the senior management before releasing/cancelling the payment.",
            height=150,
            placeholder="Describe the control you want to create test plans for...",
            help="Provide a detailed description of the control that needs to be tested"
        )
        
        # File upload section
        st.subheader("📎 Upload Context File (Optional)")
        uploaded_file = st.file_uploader(
            "Upload a CSV file with additional context data",
            type=['csv'],
            help="Upload a CSV file that contains relevant data to help generate more specific test steps"
        )
        
        # Display file preview if uploaded
        context_data = None
        if uploaded_file is not None:
            try:
                df = pd.read_csv(uploaded_file)
                st.success(f"✅ File uploaded successfully! ({df.shape[0]} rows, {df.shape[1]} columns)")
                
                # Show preview
                with st.expander("📋 Preview uploaded data"):
                    st.dataframe(df.head(10))
                
                # Convert to string for context
                context_data = df.to_string(max_rows=50, max_cols=10)
                
            except Exception as e:
                st.error(f"❌ Error reading CSV file: {str(e)}")
        
        # Generate button
        generate_button = st.button(
            "🚀 Generate Test Plans",
            type="primary",
            use_container_width=True,
            disabled=not control_description
        )
    
    with col2:
        st.header("📊 Generated Test Plan")
        
        # Output area
        if generate_button:
            if not control_description.strip():
                st.error("❌ Please enter a control description")
            else:
                with st.spinner("Generating test plan... Please wait..."):
                    test_plan = generate_test_plan(control_description, context_data)
                
                if test_plan:
                    st.success("✅ Test plan generated successfully!")
                    
                    # Display the test plan
                    st.markdown("### 📋 Test Plan Output")
                    st.markdown(test_plan)
                    
                    # Download buttons
                    col_txt, col_pdf = st.columns(2)
                    
                    with col_txt:
                        st.download_button(
                            label="� Download as Text",
                            data=test_plan,
                            file_name=f"test_plan_{control_description[:20].replace(' ', '_')}.txt",
                            mime="text/plain",
                            use_container_width=True
                        )
                    
                    with col_pdf:
                        # Generate PDF
                        pdf_buffer = create_pdf(test_plan, control_description)
                        st.download_button(
                            label="📑 Download as PDF",
                            data=pdf_buffer.getvalue(),
                            file_name=f"test_plan_{control_description[:20].replace(' ', '_')}.pdf",
                            mime="application/pdf",
                            use_container_width=True
                        )
                else:
                    st.error("❌ Failed to generate test plan. Please try again.")
        else:
            st.info("👆 Enter a control description and click 'Generate Test Plans' to see the output here")
    
    # Footer
    st.markdown("---")
    st.markdown(
        "<div style='text-align: center; color: gray;'>"
        "Test Plan Generator powered by Google Gemini AI"
        "</div>",
        unsafe_allow_html=True
    )

if __name__ == "__main__":
    main()
