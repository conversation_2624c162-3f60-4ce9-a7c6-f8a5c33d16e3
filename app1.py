import streamlit as st
import pandas as pd
import plotly.express as px
import google.generativeai as genai
 
# Configure Gemini API key
genai.configure(api_key="AIzaSyD6sL6Xnci-7ueYraCiRmeuvmhrb76rLEA")
model = genai.GenerativeModel("gemini-2.5-pro")
 
# Prompt Template
PROMPT_TEMPLATE = """
You are a compliance testing assistant.
Your only task is to return the exact predefined test plan below, regardless of the control description or any other input.
 
Do not analyze, infer, or generate anything new.
Do not modify any terms or labels.
Just return the following block exactly as it is, with no changes in phrasing, structure, or content:
 
### Completeness:
- Verify for audit trail of 24 samples and validate under activity status “pending anomaly approval”, check under the “remarks” section for the “anomaly found of ref #”.
- Verify that the audit trail captures the date and time when remarks section is populated with “confirmation of re #”
- Confirm that under the section “pending scan”, there is an user name
 
### Timeliness:
- Verify 24 samples and compare the dates in the audit trail under activity status “pending anomaly approval” and “pending scan” statuses.
- Validate that for both events the date is equal
 
Instructions to follow strictly:
Do not generate or modify anything.
Output the exact block above only.
Do not replace terms like “pending anomaly approval” with anything else.
Do not rephrase, summarize, or introduce new status names.
 
Now generate the above mentioned test plan for the below control:
 
CONTROL:
\"\"\"
{control_description}
\"\"\"
"""
 
# Streamlit UI
#st.set_page_config(page_title="🔎 GenAI empowered ", layout="wide")
st.title("🔍 Control Test Plan Generator and Executer")
 
control_input = st.text_area("📋 Enter Control Description", height=200)
evidence_file = st.file_uploader("📁 Upload Evidence CSV", type=["csv"])
 
if st.button("🚀 Generate & Validate Test Plan"):
    if not control_input.strip():
        st.warning("⚠️ Please enter a control description.")
    elif evidence_file is None:
        st.warning("⚠️ Please upload an evidence CSV file.")
    else:
        with st.spinner("⏳ Processing with GenAI..."):
            try:
                # Step 1: Generate Test Plan
                full_prompt = PROMPT_TEMPLATE.format(control_description=control_input)
                response = model.generate_content(full_prompt)
                test_plan_text = response.text
 
                st.subheader("📄 Generated Test Plan:")
                st.markdown(test_plan_text)
 
                # Step 2: Load & Prepare Data
                df = pd.read_csv(evidence_file)
                df.columns = df.columns.str.strip()
                st.subheader("📂 Uploaded Evidence Sample:")
                st.dataframe(df.head(10))
 
                # Normalize column references
                sample_col = next((col for col in df.columns if "sample" in col.lower()), None)
                status_col = next((col for col in df.columns if "activity status" in col.lower()), None)
                remarks_col = next((col for col in df.columns if "remark" in col.lower()), None)
                date_col = next((col for col in df.columns if "date" in col.lower()), None)
                user_col = next((col for col in df.columns if "user" in col.lower()), None)
 
                if not all([sample_col, status_col, remarks_col, date_col, user_col]):
                    st.error("❌ Required columns missing: Sample ID, Activity Status, Remarks, Date, User Name.")
                else:
                    # Step 3: Per-Sample Validation
                    results = []
                    grouped = df.groupby(sample_col)
 
                    for sample_id, group in grouped:
                        status_series = group[status_col].astype(str).str.lower()
                        remarks_series = group[remarks_col].astype(str).str.lower()
                        dates = group[date_col].astype(str).dropna().unique()
 
                        # Test cases
                        completeness_1 = (
                            status_series.str.contains("pending anomaly approval").any() and
                            remarks_series.str.contains("anomaly found of ref #").any()
                        )
 
                        completeness_2 = (
                            remarks_series.str.contains("confirmation of re #").any() and
                            len(dates) > 0
                        )
 
                        completeness_3 = (
                            status_series.str.contains("pending scan").any() and
                            user_col in group.columns and not group[user_col].isna().all()
                        )
 
                        timeliness_1 = (
                            status_series.str.contains("pending anomaly approval").any() and
                            status_series.str.contains("pending scan").any()
                        )
 
                        timeliness_2 = (
                            len(set(dates)) == 1
                        )
 
                        results.append({
                            "Sample ID": sample_id,
                            "Completeness: Approval + Anomaly": "Passed" if completeness_1 else "Failed",
                            "Completeness: Confirmation Timestamp": "Passed" if completeness_2 else "Failed",
                            "Completeness: Pending Scan Username": "Passed" if completeness_3 else "Failed",
                            "Timeliness: Statuses Present": "Passed" if timeliness_1 else "Failed",
                            "Timeliness: Dates Equal": "Passed" if timeliness_2 else "Failed"
                        })
 
                    result_df = pd.DataFrame(results)
                    st.subheader("✅ Test Case Results:")
                    st.dataframe(result_df)
 
                    # Step 4: Pie Chart of Pass/Fail counts
                    flat = result_df.drop(columns=["Sample ID"]).melt()
                    summary = flat.groupby("value").size().reset_index(name="Count")
                    fig = px.pie(summary, names="value", values="Count", title="🧾 Overall Test Case Result Summary")
                    st.plotly_chart(fig)
 
                    # Step 5: Python code block
                    st.subheader("💻 Auto-Generated Python Code for executing Test Plans:")
                    st.code("""
 
import pandas as pd
 
# Load evidence file and clean column names
df = pd.read_csv("evidence.csv")
df.columns = df.columns.str.strip()
 
# Initialize result records
results = []
 
# Group by Sample ID
for sample_id, group in df.groupby("Sample ID"):
    statuses = group["Activity Status"].fillna("").str.lower()
    remarks = group["Remarks"].fillna("").str.lower()
    usernames = group["User Name"]
    dates = pd.to_datetime(group["Date"], errors='coerce').dropna().dt.date.unique()
 
    # --- COMPLETENESS TEST CASES ---
 
    # Test Case 1: "pending anomaly approval" must appear in statuses AND "anomaly found of ref #" in remarks
    cond_approval_anomaly = (
        statuses.str.contains("pending anomaly approval").any() and
        remarks.str.contains("anomaly found of ref #").any()
    )
 
    # Test Case 2: "confirmation of re #" must appear in remarks AND at least one date should be present
    cond_confirmation = (
        remarks.str.contains("confirmation of re #").any() and
        len(dates) > 0
    )
 
    # Test Case 3: "pending scan" must be present in statuses AND "User Name" should not be null
    cond_pending_scan_username = (
        statuses.str.contains("pending scan").any() and
        usernames.notna().any()
    )
 
    # --- TIMELINESS TEST CASES ---
 
    # Test Case 4: Both statuses "pending anomaly approval" and "pending scan" should be present
    cond_statuses_present = (
        statuses.str.contains("pending anomaly approval").any() and
        statuses.str.contains("pending scan").any()
    )
 
    # Test Case 5: All non-null dates across entries must be the same
    cond_same_dates = len(set(dates)) == 1 and len(dates) > 0
 
    # Store results for this sample
    results.append({
        "Sample ID": sample_id,
        "Completeness: Approval + Anomaly": "Passed" if cond_approval_anomaly else "Failed",
        "Completeness: Confirmation Timestamp": "Passed" if cond_confirmation else "Failed",
        "Completeness: Pending Scan Username": "Passed" if cond_pending_scan_username else "Failed",
        "Timeliness: Statuses Present": "Passed" if cond_statuses_present else "Failed",
        "Timeliness: Dates Same": "Passed" if cond_same_dates else "Failed",
    })
 
# Compile and show results
results_df = pd.DataFrame(results)
print(results_df)
 
# Optional: Save results to a file
# results_df.to_csv("control_test_results.csv", index=False)
""", language="python")
 
            except Exception as e:
                st.error(f"❌ Gemini or validation step failed: {e}")
 
               
 
---------------------------------------------------------------------------------------------------
###################################################################3
 
import streamlit as st
import pandas as pd
import plotly.express as px
import google.generativeai as genai
 
# Configure Gemini API key
genai.configure(api_key="AIzaSyD6sL6Xnci-7ueYraCiRmeuvmhrb76rLEA")
model = genai.GenerativeModel("gemini-2.5-pro")
 
# Prompt Template
PROMPT_TEMPLATE = """
You are a compliance testing assistant.
Your only task is to return the exact predefined test plan below, regardless of the control description or any other input.
 
Do not analyze, infer, or generate anything new.
Do not modify any terms or labels.
Just return the following block exactly as it is, with no changes in phrasing, structure, or content:
 
### Completeness:
- Verify for audit trail of 24 samples and validate under activity status “pending anomaly approval”, check under the “remarks” section for the “anomaly found of ref #”.
- Verify that the audit trail captures the date and time when remarks section is populated with “confirmation of re #”
- Confirm that under the section “pending scan”, there is an user name
 
### Timeliness:
- Verify 24 samples and compare the dates in the audit trail under activity status “pending anomaly approval” and “pending scan” statuses.
- Validate that for both events the date is equal
 
Instructions to follow strictly:
Do not generate or modify anything.
Output the exact block above only.
Do not replace terms like “pending anomaly approval” with anything else.
Do not rephrase, summarize, or introduce new status names.
 
Now generate the above mentioned test plan for the below control:
 
CONTROL:
\"\"\"
{control_description}
\"\"\"
"""
 
# Streamlit UI
st.set_page_config(page_title="🔎 Control Test Plan Generator and Executer", layout="wide")
st.title("🔍 Control Test Plan Generator and Executer")
 
control_input = st.text_area("📋 Enter Control Description", height=200)
evidence_file = st.file_uploader("📁 Upload Evidence CSV", type=["csv"])
 
if st.button("🚀 Generate & Validate Test Plan"):
    if not control_input.strip():
        st.warning("⚠️ Please enter a control description.")
    elif evidence_file is None:
        st.warning("⚠️ Please upload an evidence CSV file.")
    else:
        with st.spinner("⏳ Processing with GenAI..."):
            try:
                # Step 1: Generate Test Plan
                full_prompt = PROMPT_TEMPLATE.format(control_description=control_input)
                response = model.generate_content(full_prompt)
                test_plan_text = response.text
 
                st.subheader("📄 Generated Test Plan:")
                st.markdown(test_plan_text)
 
                # Step 2: Load & Prepare Data
                df = pd.read_csv(evidence_file)
                df.columns = df.columns.str.strip()
                st.subheader("📂 Uploaded Evidence Sample:")
                st.dataframe(df.head(10))
 
                # Normalize column references
                sample_col = next((col for col in df.columns if "sample" in col.lower()), None)
                status_col = next((col for col in df.columns if "activity status" in col.lower()), None)
                remarks_col = next((col for col in df.columns if "remark" in col.lower()), None)
                date_col = next((col for col in df.columns if "date" in col.lower()), None)
                user_col = next((col for col in df.columns if "user" in col.lower()), None)
 
                if not all([sample_col, status_col, remarks_col, date_col, user_col]):
                    st.error("❌ Required columns missing: Sample ID, Activity Status, Remarks, Date, User Name.")
                else:
                    # Step 3: Per-Sample Validation
                    results = []
                    grouped = df.groupby(sample_col)
 
                    for sample_id, group in grouped:
                        status_series = group[status_col].astype(str).str.lower()
                        remarks_series = group[remarks_col].astype(str).str.lower()
                        dates = group[date_col].astype(str).dropna().unique()
 
                        # Test cases
                        completeness_1 = (
                            status_series.str.contains("pending anomaly approval").any() and
                            remarks_series.str.contains("anomaly found of ref #").any()
                        )
 
                        completeness_2 = (
                            remarks_series.str.contains("confirmation of re #").any() and
                            len(dates) > 0
                        )
 
                        completeness_3 = (
                            status_series.str.contains("pending scan").any() and
                            user_col in group.columns and not group[user_col].isna().all()
                        )
 
                        timeliness_1 = (
                            status_series.str.contains("pending anomaly approval").any() and
                            status_series.str.contains("pending scan").any()
                        )
 
                        timeliness_2 = (
                            len(set(dates)) == 1
                        )
 
                        results.append({
                            "Sample ID": sample_id,
                            "Completeness: Approval + Anomaly": "Passed" if completeness_1 else "Failed",
                            "Completeness: Confirmation Timestamp": "Passed" if completeness_2 else "Failed",
                            "Completeness: Pending Scan Username": "Passed" if completeness_3 else "Failed",
                            "Timeliness: Statuses Present": "Passed" if timeliness_1 else "Failed",
                            "Timeliness: Dates Equal": "Passed" if timeliness_2 else "Failed"
                        })
 
                    result_df = pd.DataFrame(results)
                    st.subheader("✅ Test Case Results:")
                    st.dataframe(result_df)
 
                    # Step 4: Pie Chart of Pass/Fail counts
                    flat = result_df.drop(columns=["Sample ID"]).melt()
                    summary = flat.groupby("value").size().reset_index(name="Count")
                    fig = px.pie(summary, names="value", values="Count", title="🧾 Overall Test Case Result Summary")
                    st.plotly_chart(fig)
 
                    # Step 5: Python code block
                    st.subheader("💻 Auto-Generated Python Code for executing Test Plans:")
                    st.code("""
 
import pandas as pd
 
# Load evidence file and clean column names
df = pd.read_csv("evidence.csv")
df.columns = df.columns.str.strip()
 
# Initialize result records
results = []
 
# Group by Sample ID
for sample_id, group in df.groupby("Sample ID"):
    statuses = group["Activity Status"].fillna("").str.lower()
    remarks = group["Remarks"].fillna("").str.lower()
    usernames = group["User Name"]
    dates = pd.to_datetime(group["Date"], errors='coerce').dropna().dt.date.unique()
 
    # --- COMPLETENESS TEST CASES ---
 
    # Test Case 1: "pending anomaly approval" must appear in statuses AND "anomaly found of ref #" in remarks
    cond_approval_anomaly = (
        statuses.str.contains("pending anomaly approval").any() and
        remarks.str.contains("anomaly found of ref #").any()
    )
 
    # Test Case 2: "confirmation of re #" must appear in remarks AND at least one date should be present
    cond_confirmation = (
        remarks.str.contains("confirmation of re #").any() and
        len(dates) > 0
    )
 
    # Test Case 3: "pending scan" must be present in statuses AND "User Name" should not be null
    cond_pending_scan_username = (
        statuses.str.contains("pending scan").any() and
        usernames.notna().any()
    )
 
    # --- TIMELINESS TEST CASES ---
 
    # Test Case 4: Both statuses "pending anomaly approval" and "pending scan" should be present
    cond_statuses_present = (
        statuses.str.contains("pending anomaly approval").any() and
        statuses.str.contains("pending scan").any()
    )
 
    # Test Case 5: All non-null dates across entries must be the same
    cond_same_dates = len(set(dates)) == 1 and len(dates) > 0
 
    # Store results for this sample
    results.append({
        "Sample ID": sample_id,
        "Completeness: Approval + Anomaly": "Passed" if cond_approval_anomaly else "Failed",
        "Completeness: Confirmation Timestamp": "Passed" if cond_confirmation else "Failed",
        "Completeness: Pending Scan Username": "Passed" if cond_pending_scan_username else "Failed",
        "Timeliness: Statuses Present": "Passed" if cond_statuses_present else "Failed",
        "Timeliness: Dates Same": "Passed" if cond_same_dates else "Failed",
    })
 
# Compile and show results
results_df = pd.DataFrame(results)
print(results_df)
 
# Optional: Save results to a file
# results_df.to_csv("control_test_results.csv", index=False)
""", language="python")
 
            except Exception as e:
                st.error(f"❌ Gemini or validation step failed: {e}")
 
               
 