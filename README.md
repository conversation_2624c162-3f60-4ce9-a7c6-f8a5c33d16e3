# Test Plan Generator

A Streamlit application that generates comprehensive test plans for audit controls using Google's Gemini AI.

## Features

- **User-friendly Interface**: Clean and intuitive Streamlit interface
- **AI-Powered Generation**: Uses Google Gemini AI to generate detailed test plans
- **Structured Output**: Generates test plans covering completeness, timeliness, and accuracy aspects
- **Download Functionality**: Download generated test plans as text files
- **Real-time Generation**: Generate test plans instantly with a single click

## Setup Instructions

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. Get Gemini API Key

1. Go to [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create a new API key
3. Copy the API key for use in the application

### 3. Run the Application

```bash
streamlit run app.py
```

## How to Use

1. **Enter API Key**: In the sidebar, enter your Gemini API key
2. **Describe Control**: Enter a detailed description of the control you want to test
3. **Generate**: Click the "Generate Test Plans" button
4. **Review Output**: The generated test plan will appear in the right column
5. **Download**: Optionally download the test plan as a text file

## Test Plan Structure

The generated test plans cover three main aspects:

### Completeness
- Audit trail verification for 24 samples
- Activity status validation for "pending anomaly approval"
- Remarks section checking for "anomaly found of ref #"
- Date and time capture verification
- User name confirmation in "pending scan" section

### Timeliness
- Sample verification and date comparison
- Activity status date validation
- Event date equality checks

### Accuracy
- Additional accuracy-focused test steps based on the control description

## Example Usage

1. Enter a control description like:
   ```
   Monthly reconciliation process for bank statements where all transactions 
   must be reviewed and approved within 5 business days
   ```

2. Click "Generate Test Plans"

3. Review the comprehensive test plan with specific, actionable steps

## Troubleshooting

- **API Key Issues**: Make sure your Gemini API key is valid and has proper permissions
- **Connection Problems**: Check your internet connection
- **Generation Failures**: Try refreshing the page and entering the information again

## Requirements

- Python 3.7+
- Streamlit
- Google Generative AI
- Valid Gemini API key
