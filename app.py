import streamlit as st
import pandas as pd
import plotly.express as px
import google.generativeai as genai
 
# Configure Gemini API key
genai.configure(api_key="AIzaSyD6sL6Xnci-7ueYraCiRmeuvmhrb76rLEA")
model = genai.GenerativeModel("gemini-2.5-pro")
 
# Prompt Template
PROMPT_TEMPLATE = """
You are a compliance testing assistant.
Your only task is to return the exact predefined test plan below, regardless of the control description or any other input.
 
Do not analyze, infer, or generate anything new.
Do not modify any terms or labels.
Just return the following block exactly as it is, with no changes in phrasing, structure, or content:
 
### Completeness:
- Verify for audit trail of 24 samples and validate under activity status “pending anomaly approval”, check under the “remarks” section for the “anomaly found of ref #”.
- Verify that the audit trail captures the date and time when remarks section is populated with “confirmation of re #”
- Confirm that under the section “pending scan”, there is an user name
 
### Timeliness:
- Verify 24 samples and compare the dates in the audit trail under activity status “pending anomaly approval” and “pending scan” statuses.
- Validate that for both events the date is equal
 
Instructions to follow strictly:
Do not generate or modify anything.
Output the exact block above only.
Do not replace terms like “pending anomaly approval” with anything else.
Do not rephrase, summarize, or introduce new status names.
 
Now generate the above mentioned test plan for the below control:
 
CONTROL:
\"\"\"
{control_description}
\"\"\"
"""
 
# Streamlit UI
st.set_page_config(page_title="🔎 Gemini Control Test Plan Validator", layout="wide")
st.title("🔍 Control Test Plan Generator + Validator (Per Sample)")

# Initialize session state
if 'results_data' not in st.session_state:
    st.session_state.results_data = None
if 'show_results' not in st.session_state:
    st.session_state.show_results = False
 
default_control = "(What) Investigation and resolution of systemically identified potential duplicate payments before release (How) Payment system identifies potential duplicate payments based on defined logic – Payment analyst reviews the systemic notification and disposition the outcome following business procedures. If required, payment analyst escalates potential duplicate to the senior management before releasing/cancelling the payment. "
control_input = st.text_area("📋 Enter Control Description", value=default_control, height=200)
evidence_file = st.file_uploader("📁 Upload Evidence CSV", type=["csv"])
 
if st.button("🚀 Generate & Validate Test Plan"):
    if not control_input.strip():
        st.warning("⚠️ Please enter a control description.")
    elif evidence_file is None:
        st.warning("⚠️ Please upload an evidence CSV file.")
    else:
        with st.spinner("⏳ Processing with Gemini..."):
            try:
                # Step 1: Generate Test Plan
                full_prompt = PROMPT_TEMPLATE.format(control_description=control_input)
                response = model.generate_content(full_prompt)
                test_plan_text = response.text
 
                st.subheader("📄 Generated Test Plan:")
                st.markdown(test_plan_text)
 
                # Step 2: Load & Prepare Data
                df = pd.read_csv(evidence_file)
                df.columns = df.columns.str.strip()
                st.subheader("📂 Uploaded Evidence Sample:")
                st.dataframe(df.head(10))

 
                # Step 3: Python code block (shown immediately after data preview)
                st.subheader("💻 Auto-Generated Python Code (per sample validation):")
                st.code(f"""
import pandas as pd
 
df = pd.read_csv("evidence.csv")
df.columns = df.columns.str.strip()
grouped = df.groupby("Sample ID")

# Create results list to store test outcomes
results = []

for sample_id, group in grouped:
    statuses = group["Activity Status"].str.lower()
    remarks = group["Remarks"].str.lower()
    dates = group["Date"].astype(str).dropna().unique()
    
    # Test 1: Completeness: Approval + Anomaly
    test1 = "Passed" if (statuses.str.contains("pending anomaly approval").any() and 
                        remarks.str.contains("anomaly found of ref #").any()) else "Failed"
    
    # Test 2: Completeness: Confirmation Timestamp
    test2 = "Passed" if (remarks.str.contains("confirmation of re #").any() and 
                        len(dates) > 0) else "Failed"
    
    # Test 3: Completeness: Pending Scan Username
    test3 = "Passed" if (statuses.str.contains("pending scan").any() and 
                        group["User Name"].notna().any()) else "Failed"
    
    # Test 4: Timeliness: Statuses Present
    test4 = "Passed" if (statuses.str.contains("pending anomaly approval").any() and 
                        statuses.str.contains("pending scan").any()) else "Failed"
    
    # Test 5: Timeliness: Dates Equal
    test5 = "Passed" if len(set(dates)) == 1 else "Failed"
    
    results.append({{
        "Sample ID": sample_id,
        "Completeness: Approval + Anomaly": test1,
        "Completeness: Confirmation Timestamp": test2,
        "Completeness: Pending Scan Username": test3,
        "Timeliness: Statuses Present": test4,
        "Timeliness: Dates Equal": test5
    }})

# Create results DataFrame
results_df = pd.DataFrame(results)

# Display the results table
print("✅ Test Case Results (Per Sample):")
print(results_df.to_string(index=False))
""")
                
                # Step 4: Button to run the validation
                if st.button("🚀 Run Above Python Code"):
                    # Normalize column references
                    sample_col = next((col for col in df.columns if "sample" in col.lower()), None)
                    status_col = next((col for col in df.columns if "activity status" in col.lower()), None)
                    remarks_col = next((col for col in df.columns if "remark" in col.lower()), None)
                    date_col = next((col for col in df.columns if "date" in col.lower()), None)
                    user_col = next((col for col in df.columns if "user" in col.lower()), None)
     
                    if not all([sample_col, status_col, remarks_col, date_col, user_col]):
                        st.error("❌ Required columns missing: Sample ID, Activity Status, Remarks, Date, User Name.")
                    else:
                        # Per-Sample Validation
                        results = []
                        grouped = df.groupby(sample_col)
     
                        for sample_id, group in grouped:
                            status_series = group[status_col].astype(str).str.lower()
                            remarks_series = group[remarks_col].astype(str).str.lower()
                            dates = group[date_col].astype(str).dropna().unique()
     
                            # Test cases
                            completeness_1 = (
                                status_series.str.contains("pending anomaly approval").any() and
                                remarks_series.str.contains("anomaly found of ref #").any()
                            )
     
                            completeness_2 = (
                                remarks_series.str.contains("confirmation of re #").any() and
                                len(dates) > 0
                            )
     
                            completeness_3 = (
                                status_series.str.contains("pending scan").any() and
                                user_col in group.columns and not group[user_col].isna().all()
                            )
     
                            timeliness_1 = (
                                status_series.str.contains("pending anomaly approval").any() and
                                status_series.str.contains("pending scan").any()
                            )
     
                            timeliness_2 = (
                                len(set(dates)) == 1
                            )
     
                            results.append({
                                "Sample ID": sample_id,
                                "Completeness: Approval + Anomaly": "Passed" if completeness_1 else "Failed",
                                "Completeness: Confirmation Timestamp": "Passed" if completeness_2 else "Failed",
                                "Completeness: Pending Scan Username": "Passed" if completeness_3 else "Failed",
                                "Timeliness: Statuses Present": "Passed" if timeliness_1 else "Failed",
                                "Timeliness: Dates Equal": "Passed" if timeliness_2 else "Failed"
                            })
     
                        result_df = pd.DataFrame(results)
                        
                        # Store results in session state
                        st.session_state.results_data = result_df
                        st.session_state.show_results = True
                        
                        # Force rerun to show results
                        st.rerun()

                # Display results if they exist in session state
                if st.session_state.show_results and st.session_state.results_data is not None:
                    # Reset the flag to prevent showing on every refresh
                    if st.session_state.show_results:
                        result_df = st.session_state.results_data
                        
                        # Show results in an expandable section
                        with st.expander("📊 View Test Results", expanded=True):
                            st.subheader("✅ Test Case Results (Per Sample):")
                            st.dataframe(result_df, use_container_width=True)
         
                            # Pie Chart of Pass/Fail counts
                            flat = result_df.drop(columns=["Sample ID"]).melt()
                            summary = flat.groupby("value").size().reset_index(name="Count")
                            fig = px.pie(summary, names="value", values="Count", title="🧾 Overall Test Case Result Summary")
                            st.plotly_chart(fig, use_container_width=True)
                            
                        # Also show a success message
                        st.success(f"✅ Validation completed! Processed {len(result_df)} samples.")
                        
                        # Add a button to clear results
                        if st.button("🗑️ Clear Results"):
                            st.session_state.results_data = None
                            st.session_state.show_results = False
                            st.rerun()
                        
            except Exception as e:
                st.error(f"❌ An error occurred: {str(e)}")
 