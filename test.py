import pandas as pd
 
df = pd.read_csv("evidence.csv")
df.columns = df.columns.str.strip()
grouped = df.groupby("Sample ID")

# Create results list to store test outcomes
results = []

for sample_id, group in grouped:
    statuses = group["Activity Status"].str.lower()
    remarks = group["Remarks"].str.lower()
    dates = group["Date"].astype(str).dropna().unique()
    
    # Test 1: Completeness: Approval + Anomaly
    test1 = "Passed" if (statuses.str.contains("pending anomaly approval").any() and 
                        remarks.str.contains("anomaly found of ref #").any()) else "Failed"
    
    # Test 2: Completeness: Confirmation Timestamp
    test2 = "Passed" if (remarks.str.contains("confirmation of re #").any() and 
                        len(dates) > 0) else "Failed"
    
    # Test 3: Completeness: Pending Scan Username
    test3 = "Passed" if (statuses.str.contains("pending scan").any() and 
                        group["User Name"].notna().any()) else "Failed"
    
    # Test 4: Timeliness: Statuses Present
    test4 = "Passed" if (statuses.str.contains("pending anomaly approval").any() and 
                        statuses.str.contains("pending scan").any()) else "Failed"
    
    # Test 5: Timeliness: Dates Equal
    test5 = "Passed" if len(set(dates)) == 1 else "Failed"
    
    results.append({
        "Sample ID": sample_id,
        "Completeness: Approval + Anomaly": test1,
        "Completeness: Confirmation Timestamp": test2,
        "Completeness: Pending Scan Username": test3,
        "Timeliness: Statuses Present": test4,
        "Timeliness: Dates Equal": test5
    })

# Create results DataFrame
results_df = pd.DataFrame(results)

# Display the results table
print("✅ Test Case Results (Per Sample):")
print(results_df.to_string(index=False))

# Also display original data for reference
print("\n" + "="*50)
print("Original Data:")
print(df.to_string(index=False))